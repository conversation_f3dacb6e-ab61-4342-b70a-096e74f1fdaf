#!/usr/bin/env python3
"""
测试XPath与URL绑定关系修复效果
"""

import json
import sys
import os
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.models.xpath_result import XPathRuleItem, ClassifiedXPathResult


def test_xpath_rule_item():
    """测试XPathRuleItem类"""
    print("=" * 60)
    print("测试XPathRuleItem类")
    print("=" * 60)
    
    # 创建测试数据
    rule = XPathRuleItem(
        xpath="//div[@class='news']//a[@href]",
        source_url="https://example.com/investors",
        page_type="investor_relations",
        confidence=0.85
    )
    
    print(f"XPath规则: {rule.xpath}")
    print(f"来源URL: {rule.source_url}")
    print(f"页面类型: {rule.page_type}")
    print(f"置信度: {rule.confidence}")
    print(f"创建时间: {rule.created_at}")
    
    # 测试转换为字典
    rule_dict = rule.to_dict()
    print(f"\n转换为字典: {json.dumps(rule_dict, indent=2, ensure_ascii=False)}")
    
    # 测试从字典创建
    rule2 = XPathRuleItem.from_dict(rule_dict)
    print(f"\n从字典重建: {rule2.xpath} -> {rule2.source_url}")
    
    print("✓ XPathRuleItem测试通过\n")


def test_classified_xpath_result():
    """测试ClassifiedXPathResult类"""
    print("=" * 60)
    print("测试ClassifiedXPathResult类")
    print("=" * 60)
    
    # 创建测试实例
    result = ClassifiedXPathResult()
    
    # 添加测试数据
    test_data = [
        {
            'xpath_list': ["//div[@class='investor-news']//a[@href]", "//section[@id='financial-reports']//li//a"],
            'source_url': "https://investors.example.com/investor-relations",
            'page_type': "investor_relations",
            'rule_type': "investor",
            'confidence': 0.90
        },
        {
            'xpath_list': ["//div[@class='company-news']//a[@href]", "//ul[@id='press-releases']//li//a"],
            'source_url': "https://investors.example.com/news-events/press-releases",
            'page_type': "investor_relations",
            'rule_type': "news",
            'confidence': 0.85
        },
        {
            'xpath_list': ["//div[@class='news-list']//a[@href]"],
            'source_url': "https://example.com/news",
            'page_type': "news_section",
            'rule_type': "general",
            'confidence': 0.75
        },
        {
            'xpath_list': ["//div[@class='investor-news']//a[@href]"],  # 重复规则，测试去重
            'source_url': "https://investors.example.com/another-page",
            'page_type': "investor_relations",
            'rule_type': "investor",
            'confidence': 0.80
        }
    ]
    
    # 添加规则
    for data in test_data:
        result.add_rules(
            data['xpath_list'],
            data['source_url'],
            data['page_type'],
            data['rule_type'],
            data['confidence']
        )
    
    print(f"添加规则后的统计信息:")
    stats = result.get_statistics()
    print(json.dumps(stats, indent=2, ensure_ascii=False))
    
    # 测试按URL查询
    print(f"\n按URL查询规则:")
    url_rules = result.get_rules_by_url("https://investors.example.com/investor-relations")
    print(f"URL: https://investors.example.com/investor-relations")
    print(json.dumps(url_rules, indent=2, ensure_ascii=False))
    
    # 测试URL到XPath的映射
    print(f"\nURL到XPath映射:")
    mapping = result.get_url_xpath_mapping()
    print(json.dumps(mapping, indent=2, ensure_ascii=False))
    
    # 测试去重
    print(f"\n去重前统计: {result.get_statistics()['total_rules']} 个规则")
    result.deduplicate()
    print(f"去重后统计: {result.get_statistics()['total_rules']} 个规则")
    
    # 测试跨类别去重
    result.remove_cross_category_duplicates()
    print(f"跨类别去重后统计: {result.get_statistics()['total_rules']} 个规则")
    
    # 测试向后兼容格式
    print(f"\n向后兼容格式:")
    legacy = result.get_legacy_format()
    print(json.dumps(legacy, indent=2, ensure_ascii=False))
    
    print("✓ ClassifiedXPathResult测试通过\n")


def test_xpath_url_binding_scenario():
    """测试实际场景中的xpath与URL绑定"""
    print("=" * 60)
    print("测试实际场景中的xpath与URL绑定")
    print("=" * 60)
    
    # 模拟实际的分析结果
    result = ClassifiedXPathResult()
    
    # 模拟从不同URL提取的xpath规则
    scenarios = [
        {
            'url': 'https://investors.trisaluslifesci.com/investor-relations',
            'page_type': 'investor_relations',
            'investor_xpath': [
                "//div[contains(@class, 'block--nir-news__widget')]//a[@href]",
                "//div[contains(@class, 'nir-widget--news--headline')]//a"
            ],
            'news_xpath': [],
            'general_xpath': []
        },
        {
            'url': 'https://investors.trisaluslifesci.com/news-events/press-releases',
            'page_type': 'investor_relations',
            'investor_xpath': [],
            'news_xpath': [
                "//div[contains(@class, 'nir-widget--list')]//a[@href]",
                "//article[contains(@class, 'node--nir-news--nir-widget-list')]//a[@href]"
            ],
            'general_xpath': []
        },
        {
            'url': 'https://investors.trisaluslifesci.com/financials/sec-filings',
            'page_type': 'investor_relations',
            'investor_xpath': [
                "//div[@class='view-content']//tr//td[2]//a[@href]",
                "//table[contains(@class, 'views-table')]//tr//td[2]//a"
            ],
            'news_xpath': [],
            'general_xpath': []
        }
    ]
    
    # 添加规则
    for scenario in scenarios:
        result.add_rules(scenario['investor_xpath'], scenario['url'], scenario['page_type'], 'investor', 0.90)
        result.add_rules(scenario['news_xpath'], scenario['url'], scenario['page_type'], 'news', 0.85)
        result.add_rules(scenario['general_xpath'], scenario['url'], scenario['page_type'], 'general', 0.80)
    
    # 验证绑定关系
    print("验证xpath与URL的绑定关系:")
    print("-" * 40)
    
    for scenario in scenarios:
        url = scenario['url']
        print(f"\nURL: {url}")
        url_rules = result.get_rules_by_url(url)
        
        for rule_type in ['investor_xpath_rules', 'news_xpath_rules', 'general_xpath_rules']:
            rules = url_rules[rule_type]
            if rules:
                print(f"  {rule_type}: {len(rules)} 个规则")
                for rule in rules:
                    print(f"    - {rule}")
    
    # 验证可追溯性
    print(f"\n验证可追溯性:")
    print("-" * 40)
    
    all_rules_with_sources = result.get_all_rules_with_sources()
    for rule_type, rules in all_rules_with_sources.items():
        if rules:
            print(f"\n{rule_type}:")
            for rule in rules:
                print(f"  XPath: {rule['xpath']}")
                print(f"  来源: {rule['source_url']}")
                print(f"  类型: {rule['page_type']}")
                print(f"  置信度: {rule['confidence']}")
                print()
    
    # 生成最终结果
    final_result = {
        'company_name': 'TriSalus',
        'xpath_rules_with_sources': all_rules_with_sources,
        'url_xpath_mapping': result.get_url_xpath_mapping(),
        'legacy_format': result.get_legacy_format(),
        'statistics': result.get_statistics()
    }
    
    print("最终结果结构:")
    print("-" * 40)
    print(json.dumps(final_result, indent=2, ensure_ascii=False))
    
    print("✓ 实际场景测试通过\n")


def main():
    """主测试函数"""
    print("开始测试XPath与URL绑定关系修复效果")
    print("测试时间:", "2025-07-03")
    print("=" * 80)
    
    try:
        # 运行各项测试
        test_xpath_rule_item()
        test_classified_xpath_result()
        test_xpath_url_binding_scenario()
        
        print("=" * 80)
        print("✅ 所有测试通过！XPath与URL绑定关系修复成功")
        print("=" * 80)
        
        print("\n修复效果总结:")
        print("1. ✓ 每个xpath规则都能追溯到其来源URL")
        print("2. ✓ xpath规则与URL的关联关系得到正确维护")
        print("3. ✓ 支持按页面类型和来源URL进行xpath规则分类")
        print("4. ✓ 保持现有API和数据格式的兼容性")
        print("5. ✓ 便于调试和问题排查")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
