"""
AI分析服务
"""
import json
import re
import time
from typing import Optional, Dict, Any, List
from openai import OpenAI
from src.config import Config
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

class AIAnalyzerService:
    """AI分析服务类"""
    
    def __init__(self):
        """初始化AI分析服务"""
        self.client = OpenAI(
            api_key=Config.OPENAI_API_KEY,
            base_url=Config.OPENAI_BASE_URL,
            timeout=Config.OPENAI_TIMEOUT
        )
        self.model = Config.OPENAI_MODEL
        self.max_retries = Config.MAX_RETRIES
        
        if not Config.OPENAI_API_KEY:
            raise ValueError("OpenAI API Key未配置")
        
        logger.info("AI分析服务初始化完成")
    
    def analyze(self, prompt: str, temperature: float = 0.1) -> Optional[str]:
        """
        使用AI分析内容
        
        Args:
            prompt: 分析提示词
            temperature: 温度参数，控制输出的随机性
            
        Returns:
            AI分析结果，失败返回None
        """
        logger.info("开始AI分析")
        
        for attempt in range(self.max_retries):
            try:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {
                            "role": "system",
                            "content": "你是一个专业的网页分析专家，擅长从网页内容中提取结构化信息。请严格按照要求返回JSON格式的结果。"
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    temperature=temperature,
                    max_tokens=4000
                )
                
                result = response.choices[0].message.content
                logger.info(f"AI分析完成，结果长度: {len(result) if result else 0}")
                
                return result
                
            except Exception as e:
                logger.error(f"AI分析失败，尝试 {attempt + 1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(2 * (attempt + 1))  # 递增延迟
                    continue
                else:
                    logger.error("AI分析最终失败")
                    return None
        
        return None
    
    def analyze_with_json_validation(self, prompt: str, expected_keys: list = None) -> Optional[Dict[str, Any]]:
        """
        使用AI分析内容并验证JSON格式
        
        Args:
            prompt: 分析提示词
            expected_keys: 期望的JSON键列表
            
        Returns:
            解析后的JSON字典，失败返回None
        """
        result = self.analyze(prompt)
        if not result:
            return None
        
        try:
            # 尝试解析JSON
            json_result = json.loads(result)
            
            # 验证期望的键
            if expected_keys:
                missing_keys = [key for key in expected_keys if key not in json_result]
                if missing_keys:
                    logger.warning(f"AI返回的JSON缺少期望的键: {missing_keys}")
            
            return json_result
            
        except json.JSONDecodeError as e:
            logger.error(f"AI返回结果不是有效的JSON: {e}")
            logger.error(f"AI返回内容: {result}")  # 改为ERROR级别以便看到

            # 尝试修复JSON格式
            fixed_result = self._try_fix_json(result)
            if fixed_result:
                logger.info("JSON修复成功")
                return fixed_result

            logger.error("JSON修复失败")
            return None
    
    def _try_fix_json(self, content: str) -> Optional[Dict[str, Any]]:
        """
        尝试修复损坏的JSON格式

        Args:
            content: 可能损坏的JSON内容

        Returns:
            修复后的JSON字典，失败返回None
        """
        try:
            # 清理内容
            content = content.strip()

            # 尝试直接解析
            try:
                return json.loads(content)
            except:
                pass

            # 尝试提取JSON部分
            start_idx = content.find('{')
            end_idx = content.rfind('}')

            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                json_part = content[start_idx:end_idx + 1]
                try:
                    return json.loads(json_part)
                except:
                    pass

            # 尝试查找```json代码块
            json_match = re.search(r'```json\s*\n(.*?)\n```', content, re.DOTALL)
            if json_match:
                json_content = json_match.group(1).strip()
                try:
                    return json.loads(json_content)
                except:
                    pass

            # 尝试查找```代码块
            code_match = re.search(r'```\s*\n(.*?)\n```', content, re.DOTALL)
            if code_match:
                code_content = code_match.group(1).strip()
                if code_content.startswith('{') and code_content.endswith('}'):
                    try:
                        return json.loads(code_content)
                    except:
                        pass

        except Exception as e:
            logger.debug(f"JSON修复失败: {e}")

        return None
    
    def analyze_search_results(self, company_name: str, search_results: list) -> Optional[Dict[str, Any]]:
        """
        分析搜索结果，找出官方网站
        
        Args:
            company_name: 公司名称
            search_results: 搜索结果列表
            
        Returns:
            分析结果字典
        """
        from src.utils.prompt_manager import PromptManager
        
        pm = PromptManager()
        prompt = pm.load_prompt(
            'search/find_official_website.txt',
            {
                'company_name': company_name,
                'search_results': json.dumps(search_results, ensure_ascii=False, indent=2)
            }
        )
        
        return self.analyze_with_json_validation(
            prompt,
            expected_keys=['official_website', 'confidence', 'reasoning']
        )
    
    def analyze_investor_relations(self, company_name: str, base_url: str, html_content: str) -> Optional[Dict[str, Any]]:
        """
        分析网页内容，提取投资者关系页面链接
        
        Args:
            company_name: 公司名称
            base_url: 基础URL
            html_content: HTML内容
            
        Returns:
            分析结果字典
        """
        from src.utils.prompt_manager import PromptManager
        
        pm = PromptManager()
        prompt = pm.load_prompt(
            'analysis/extract_investor_relations.txt',
            {
                'company_name': company_name,
                'base_url': base_url,
                'html_content': html_content[:50000]  # 限制内容长度
            }
        )
        
        return self.analyze_with_json_validation(
            prompt,
            expected_keys=['investor_relations_urls', 'confidence']
        )
    
    def analyze_news_xpath(self, company_name: str, page_url: str, html_content: str) -> Optional[Dict[str, Any]]:
        """
        分析投资者关系页面，提取新闻链接的XPath规则
        
        Args:
            company_name: 公司名称
            page_url: 页面URL
            html_content: HTML内容
            
        Returns:
            分析结果字典
        """
        from src.utils.prompt_manager import PromptManager
        
        pm = PromptManager()
        prompt = pm.load_prompt(
            'extraction/extract_news_xpath.txt',
            {
                'company_name': company_name,
                'page_url': page_url,
                'html_content': html_content[:50000]  # 限制内容长度
            }
        )
        
        return self.analyze_with_json_validation(
            prompt,
            expected_keys=['xpath_rules', 'confidence']
        )

    def analyze_investor_relations_batch(self, company_name: str, base_url: str, html_fragments: List[str]) -> Optional[Dict[str, Any]]:
        """
        分批分析网页内容，提取投资者关系页面链接

        Args:
            company_name: 公司名称
            base_url: 基础URL
            html_fragments: HTML内容分片列表

        Returns:
            合并后的分析结果字典
        """
        logger.info(f"开始分批分析投资者关系页面，共 {len(html_fragments)} 个分片")

        all_urls = []
        total_confidence = 0
        valid_results = 0

        for i, fragment in enumerate(html_fragments, 1):
            try:
                logger.debug(f"分析分片 {i}/{len(html_fragments)} (长度: {len(fragment):,} 字符)")

                # 分析单个分片
                result = self.analyze_investor_relations(company_name, base_url, fragment)

                if result:
                    urls = result.get('investor_relations_urls', [])
                    confidence = result.get('confidence', 0)

                    if urls and confidence > 0.2:  # 降低单片置信度要求
                        all_urls.extend(urls)
                        total_confidence += confidence
                        valid_results += 1
                        logger.debug(f"分片 {i} 提取到 {len(urls)} 个URL，置信度: {confidence:.2f}")
                    else:
                        logger.debug(f"分片 {i} 结果质量较低，跳过")
                else:
                    logger.warning(f"分片 {i} 分析失败")

            except Exception as e:
                logger.error(f"分析分片 {i} 时出错: {e}")
                continue

        # 合并结果
        if valid_results > 0:
            # 去重URL（保持顺序）
            unique_urls = list(dict.fromkeys(all_urls))

            # 进一步优化：移除冗余URL
            unique_urls = self._optimize_urls(unique_urls)

            avg_confidence = total_confidence / valid_results

            logger.info(f"分批分析完成: {len(all_urls)} -> {len(unique_urls)} 个唯一URL，平均置信度: {avg_confidence:.2f}")

            return {
                'investor_relations_urls': unique_urls,
                'confidence': avg_confidence,
                'batch_info': {
                    'total_fragments': len(html_fragments),
                    'valid_results': valid_results,
                    'total_urls_found': len(all_urls),
                    'unique_urls': len(unique_urls)
                }
            }
        else:
            logger.warning("所有分片分析都失败")
            return None

    def analyze_news_xpath_batch(self, company_name: str, page_url: str, html_fragments: List[str]) -> Optional[Dict[str, Any]]:
        """
        分批分析投资者关系页面，提取新闻链接的XPath规则

        Args:
            company_name: 公司名称
            page_url: 页面URL
            html_fragments: HTML内容分片列表

        Returns:
            合并后的分析结果字典
        """
        logger.info(f"开始分批分析XPath规则，共 {len(html_fragments)} 个分片")

        all_primary_xpath = []
        all_xpath_rules = []
        total_confidence = 0
        valid_results = 0

        for i, fragment in enumerate(html_fragments, 1):
            try:
                logger.debug(f"分析分片 {i}/{len(html_fragments)} (长度: {len(fragment):,} 字符)")

                # 分析单个分片
                result = self.analyze_news_xpath(company_name, page_url, fragment)

                if result:
                    xpath_rules = result.get('xpath_rules', [])
                    confidence = result.get('confidence', 0)

                    if xpath_rules and confidence > 0.3:  # 降低单片置信度要求
                        # 分离primary_xpath和其他xpath_rules
                        primary_xpath = []
                        other_rules = []

                        for rule in xpath_rules:
                            # 判断是否为主要XPath（通常包含新闻列表的容器）
                            if any(keyword in rule.lower() for keyword in [
                                'news', 'press', 'release', 'announcement', 'article',
                                'list', 'grid', 'container', 'widget', 'block'
                            ]):
                                primary_xpath.append(rule)
                            else:
                                other_rules.append(rule)

                        all_primary_xpath.extend(primary_xpath)
                        all_xpath_rules.extend(other_rules)
                        total_confidence += confidence
                        valid_results += 1

                        logger.debug(f"分片 {i} 提取到 {len(primary_xpath)} 个主要XPath，{len(other_rules)} 个其他规则，置信度: {confidence:.2f}")
                    else:
                        logger.debug(f"分片 {i} 结果质量较低，跳过")
                else:
                    logger.warning(f"分片 {i} 分析失败")

            except Exception as e:
                logger.error(f"分析分片 {i} 时出错: {e}")
                continue

        # 合并结果
        if valid_results > 0:
            # 去重XPath规则（保持顺序）
            unique_primary_xpath = list(dict.fromkeys(all_primary_xpath))
            unique_xpath_rules = list(dict.fromkeys(all_xpath_rules))

            # 进一步优化：移除冗余和冲突的规则
            unique_primary_xpath = self._optimize_xpath_rules(unique_primary_xpath)
            unique_xpath_rules = self._optimize_xpath_rules(unique_xpath_rules)

            # 确保primary_xpath和xpath_rules之间没有重复
            unique_xpath_rules = [rule for rule in unique_xpath_rules if rule not in unique_primary_xpath]

            avg_confidence = total_confidence / valid_results

            logger.info(f"分批XPath分析完成:")
            logger.info(f"  主要XPath: {len(all_primary_xpath)} -> {len(unique_primary_xpath)} 个唯一规则")
            logger.info(f"  其他规则: {len(all_xpath_rules)} -> {len(unique_xpath_rules)} 个唯一规则")
            logger.info(f"  平均置信度: {avg_confidence:.2f}")

            return {
                'primary_xpath': unique_primary_xpath,
                'xpath_rules': unique_xpath_rules,
                'confidence': avg_confidence,
                'batch_info': {
                    'total_fragments': len(html_fragments),
                    'valid_results': valid_results,
                    'total_primary_found': len(all_primary_xpath),
                    'total_rules_found': len(all_xpath_rules),
                    'unique_primary': len(unique_primary_xpath),
                    'unique_rules': len(unique_xpath_rules)
                }
            }
        else:
            logger.warning("所有分片XPath分析都失败")
            return None

    def _optimize_xpath_rules(self, xpath_rules: List[str]) -> List[str]:
        """
        优化XPath规则，移除冗余和冲突的规则

        Args:
            xpath_rules: 原始XPath规则列表

        Returns:
            优化后的XPath规则列表
        """
        if not xpath_rules:
            return []

        optimized_rules = []

        for rule in xpath_rules:
            # 跳过空规则
            if not rule or not rule.strip():
                continue

            rule = rule.strip()

            # 检查是否与已有规则冲突或冗余
            is_redundant = False

            for existing_rule in optimized_rules:
                # 检查是否完全相同
                if rule == existing_rule:
                    is_redundant = True
                    break

                # 检查是否为子集（更具体的规则）
                if self._is_xpath_subset(rule, existing_rule):
                    # 当前规则更具体，替换现有规则
                    optimized_rules.remove(existing_rule)
                    break
                elif self._is_xpath_subset(existing_rule, rule):
                    # 现有规则更具体，跳过当前规则
                    is_redundant = True
                    break

            if not is_redundant:
                optimized_rules.append(rule)

        logger.debug(f"XPath规则优化: {len(xpath_rules)} -> {len(optimized_rules)}")
        return optimized_rules

    def _is_xpath_subset(self, xpath1: str, xpath2: str) -> bool:
        """
        检查xpath1是否为xpath2的子集（更具体的规则）

        Args:
            xpath1: 第一个XPath规则
            xpath2: 第二个XPath规则

        Returns:
            xpath1是否为xpath2的子集
        """
        try:
            # 简单的启发式检查
            # 如果xpath1包含xpath2的所有部分，且xpath1更长，则认为xpath1更具体
            if xpath2 in xpath1 and len(xpath1) > len(xpath2):
                return True

            # 检查是否为相同元素的不同属性选择器
            if '//' in xpath1 and '//' in xpath2:
                base1 = xpath1.split('//')[1].split('[')[0] if '[' in xpath1 else xpath1.split('//')[1]
                base2 = xpath2.split('//')[1].split('[')[0] if '[' in xpath2 else xpath2.split('//')[1]

                if base1 == base2 and len(xpath1) > len(xpath2):
                    return True

            return False

        except Exception:
            return False

    def _optimize_urls(self, urls: List[str]) -> List[str]:
        """
        优化URL列表，移除冗余和无效的URL

        Args:
            urls: 原始URL列表

        Returns:
            优化后的URL列表
        """
        if not urls:
            return []

        optimized_urls = []

        for url in urls:
            # 跳过空URL
            if not url or not url.strip():
                continue

            url = url.strip()

            # 跳过明显无效的URL
            if not self._is_valid_investor_url(url):
                continue

            # 检查是否与已有URL冗余
            is_redundant = False

            for existing_url in optimized_urls:
                # 检查是否完全相同
                if url == existing_url:
                    is_redundant = True
                    break

                # 检查是否为相同页面的不同参数版本
                if self._are_similar_urls(url, existing_url):
                    # 保留更简洁的URL
                    if len(url) < len(existing_url):
                        optimized_urls.remove(existing_url)
                        break
                    else:
                        is_redundant = True
                        break

            if not is_redundant:
                optimized_urls.append(url)

        logger.debug(f"URL优化: {len(urls)} -> {len(optimized_urls)}")
        return optimized_urls

    def _is_valid_investor_url(self, url: str) -> bool:
        """
        检查URL是否为有效的投资者关系URL

        Args:
            url: 要检查的URL

        Returns:
            是否为有效的投资者关系URL
        """
        try:
            # 基本URL格式检查
            if not url.startswith(('http://', 'https://')):
                return False

            # 检查是否包含投资者关系相关关键词
            investor_keywords = [
                'investor', 'ir', 'financial', 'sec', 'filing', 'earnings',
                'news', 'press', 'release', 'announcement', 'governance',
                'annual', 'quarterly', 'report', 'stock', 'shareholder'
            ]

            url_lower = url.lower()
            return any(keyword in url_lower for keyword in investor_keywords)

        except Exception:
            return False

    def _are_similar_urls(self, url1: str, url2: str) -> bool:
        """
        检查两个URL是否相似（可能是同一页面的不同版本）

        Args:
            url1: 第一个URL
            url2: 第二个URL

        Returns:
            两个URL是否相似
        """
        try:
            from urllib.parse import urlparse

            parsed1 = urlparse(url1)
            parsed2 = urlparse(url2)

            # 检查域名和路径是否相同
            if parsed1.netloc == parsed2.netloc and parsed1.path == parsed2.path:
                return True

            # 检查是否为相同路径的不同参数版本
            if (parsed1.netloc == parsed2.netloc and
                parsed1.path.rstrip('/') == parsed2.path.rstrip('/')):
                return True

            return False

        except Exception:
            return False

    def analyze_news_sections(self, company_name: str, base_url: str, html_content: str) -> Optional[Dict[str, Any]]:
        """
        分析网页内容，提取新闻板块页面链接（区分投资者关系和一般新闻）

        Args:
            company_name: 公司名称
            base_url: 基础URL
            html_content: HTML内容

        Returns:
            分析结果字典，包含分类的新闻板块信息
        """
        from src.utils.prompt_manager import PromptManager

        pm = PromptManager()
        prompt = pm.load_prompt(
            'analysis/extract_news_sections.txt',
            {
                'company_name': company_name,
                'base_url': base_url,
                'html_content': html_content[:50000]  # 限制内容长度
            }
        )

        return self.analyze_with_json_validation(
            prompt,
            expected_keys=['investor_relations_urls', 'news_section_urls', 'mixed_section_urls', 'confidence']
        )

    def analyze_news_sections_batch(self, company_name: str, base_url: str, html_fragments: List[str]) -> Optional[Dict[str, Any]]:
        """
        分批分析网页内容，提取新闻板块页面链接

        Args:
            company_name: 公司名称
            base_url: 基础URL
            html_fragments: HTML内容分片列表

        Returns:
            合并后的分析结果字典
        """
        logger.info(f"开始分批分析新闻板块，共 {len(html_fragments)} 个分片")

        all_investor_urls = []
        all_news_urls = []
        all_mixed_urls = []
        total_confidence = 0
        valid_results = 0

        for i, fragment in enumerate(html_fragments, 1):
            try:
                logger.debug(f"分析分片 {i}/{len(html_fragments)} (长度: {len(fragment):,} 字符)")

                # 分析单个分片
                result = self.analyze_news_sections(company_name, base_url, fragment)

                if result:
                    investor_urls = result.get('investor_relations_urls', [])
                    news_urls = result.get('news_section_urls', [])
                    mixed_urls = result.get('mixed_section_urls', [])
                    confidence = result.get('confidence', 0)

                    if (investor_urls or news_urls or mixed_urls) and confidence > 0.2:
                        all_investor_urls.extend(investor_urls)
                        all_news_urls.extend(news_urls)
                        all_mixed_urls.extend(mixed_urls)
                        total_confidence += confidence
                        valid_results += 1

                        logger.debug(f"分片 {i} 提取到: 投资者{len(investor_urls)}个, 新闻{len(news_urls)}个, 混合{len(mixed_urls)}个, 置信度: {confidence:.2f}")
                    else:
                        logger.debug(f"分片 {i} 结果质量较低，跳过")
                else:
                    logger.warning(f"分片 {i} 分析失败")

            except Exception as e:
                logger.error(f"分析分片 {i} 时出错: {e}")
                continue

        # 合并结果
        if valid_results > 0:
            # 去重URL（保持顺序）
            unique_investor_urls = list(dict.fromkeys(all_investor_urls))
            unique_news_urls = list(dict.fromkeys(all_news_urls))
            unique_mixed_urls = list(dict.fromkeys(all_mixed_urls))

            # 进一步优化：移除冗余URL
            unique_investor_urls = self._optimize_urls(unique_investor_urls)
            unique_news_urls = self._optimize_news_urls(unique_news_urls)
            unique_mixed_urls = self._optimize_urls(unique_mixed_urls)

            avg_confidence = total_confidence / valid_results

            logger.info(f"分批新闻板块分析完成:")
            logger.info(f"  投资者关系: {len(all_investor_urls)} -> {len(unique_investor_urls)} 个唯一URL")
            logger.info(f"  新闻板块: {len(all_news_urls)} -> {len(unique_news_urls)} 个唯一URL")
            logger.info(f"  混合板块: {len(all_mixed_urls)} -> {len(unique_mixed_urls)} 个唯一URL")
            logger.info(f"  平均置信度: {avg_confidence:.2f}")

            return {
                'investor_relations_urls': unique_investor_urls,
                'news_section_urls': unique_news_urls,
                'mixed_section_urls': unique_mixed_urls,
                'confidence': avg_confidence,
                'batch_info': {
                    'total_fragments': len(html_fragments),
                    'valid_results': valid_results,
                    'total_investor_found': len(all_investor_urls),
                    'total_news_found': len(all_news_urls),
                    'total_mixed_found': len(all_mixed_urls),
                    'unique_investor': len(unique_investor_urls),
                    'unique_news': len(unique_news_urls),
                    'unique_mixed': len(unique_mixed_urls)
                }
            }
        else:
            logger.warning("所有分片新闻板块分析都失败")
            return None

    def _optimize_news_urls(self, urls: List[str]) -> List[str]:
        """
        优化新闻URL列表，移除冗余和无效的URL

        Args:
            urls: 原始新闻URL列表

        Returns:
            优化后的新闻URL列表
        """
        if not urls:
            return []

        optimized_urls = []

        for url in urls:
            # 跳过空URL
            if not url or not url.strip():
                continue

            url = url.strip()

            # 跳过明显无效的URL
            if not self._is_valid_news_url(url):
                continue

            # 检查是否与已有URL冗余
            is_redundant = False

            for existing_url in optimized_urls:
                # 检查是否完全相同
                if url == existing_url:
                    is_redundant = True
                    break

                # 检查是否为相同页面的不同参数版本
                if self._are_similar_urls(url, existing_url):
                    # 保留更简洁的URL
                    if len(url) < len(existing_url):
                        optimized_urls.remove(existing_url)
                        break
                    else:
                        is_redundant = True
                        break

            if not is_redundant:
                optimized_urls.append(url)

        logger.debug(f"新闻URL优化: {len(urls)} -> {len(optimized_urls)}")
        return optimized_urls

    def _is_valid_news_url(self, url: str) -> bool:
        """
        检查URL是否为有效的新闻URL

        Args:
            url: 要检查的URL

        Returns:
            是否为有效的新闻URL
        """
        try:
            # 基本URL格式检查
            if not url.startswith(('http://', 'https://')):
                return False

            # 检查是否包含新闻相关关键词
            news_keywords = [
                'news', 'press', 'media', 'announcement', 'blog', 'updates',
                'events', 'stories', 'article', 'release', 'publication',
                'newsroom', 'press-release', 'media-center', 'company-news',
                'latest', 'recent', 'current', 'today', 'breaking'
            ]

            url_lower = url.lower()
            return any(keyword in url_lower for keyword in news_keywords)

        except Exception:
            return False

    def _classify_url_type(self, url: str) -> str:
        """
        分类URL类型（投资者关系、新闻、混合）

        Args:
            url: 要分类的URL

        Returns:
            URL类型：'investor', 'news', 'mixed', 'unknown'
        """
        try:
            url_lower = url.lower()

            # 投资者关系关键词
            investor_keywords = [
                'investor', 'ir', 'financial', 'sec', 'filing', 'earnings',
                'quarterly', 'annual', 'report', 'stock', 'shareholder',
                'governance', 'dividend', 'proxy', 'compliance'
            ]

            # 新闻关键词
            news_keywords = [
                'news', 'press', 'media', 'announcement', 'blog', 'updates',
                'events', 'stories', 'article', 'publication', 'newsroom',
                'press-release', 'media-center', 'company-news', 'latest'
            ]

            # 检查关键词匹配
            has_investor = any(keyword in url_lower for keyword in investor_keywords)
            has_news = any(keyword in url_lower for keyword in news_keywords)

            if has_investor and has_news:
                return 'mixed'
            elif has_investor:
                return 'investor'
            elif has_news:
                return 'news'
            else:
                return 'unknown'

        except Exception:
            return 'unknown'

    def analyze_classified_news_xpath(self, company_name: str, page_url: str, page_type: str, html_content: str) -> Optional[Dict[str, Any]]:
        """
        分析新闻板块页面，提取分类的新闻链接XPath规则

        Args:
            company_name: 公司名称
            page_url: 页面URL
            page_type: 页面类型 (investor_relations, news_section, mixed_section)
            html_content: HTML内容

        Returns:
            分析结果字典，包含分类的XPath规则
        """
        from src.utils.prompt_manager import PromptManager

        pm = PromptManager()
        prompt = pm.load_prompt(
            'extraction/extract_classified_news_xpath.txt',
            {
                'company_name': company_name,
                'page_url': page_url,
                'page_type': page_type,
                'html_content': html_content[:50000]  # 限制内容长度
            }
        )

        return self.analyze_with_json_validation(
            prompt,
            expected_keys=['investor_xpath_rules', 'news_xpath_rules', 'general_xpath_rules', 'confidence']
        )

    def analyze_classified_news_xpath_batch(self, company_name: str, page_url: str, page_type: str, html_fragments: List[str]) -> Optional[Dict[str, Any]]:
        """
        分批分析新闻板块页面，提取分类的新闻链接XPath规则

        Args:
            company_name: 公司名称
            page_url: 页面URL
            page_type: 页面类型
            html_fragments: HTML内容分片列表

        Returns:
            合并后的分析结果字典
        """
        logger.info(f"开始分批分析分类XPath规则，页面类型: {page_type}，共 {len(html_fragments)} 个分片")

        all_investor_xpath = []
        all_news_xpath = []
        all_general_xpath = []
        total_confidence = 0
        valid_results = 0

        for i, fragment in enumerate(html_fragments, 1):
            try:
                logger.debug(f"分析分片 {i}/{len(html_fragments)} (长度: {len(fragment):,} 字符)")

                # 分析单个分片
                result = self.analyze_classified_news_xpath(company_name, page_url, page_type, fragment)

                if result:
                    investor_xpath = result.get('investor_xpath_rules', [])
                    news_xpath = result.get('news_xpath_rules', [])
                    general_xpath = result.get('general_xpath_rules', [])
                    confidence = result.get('confidence', 0)

                    if (investor_xpath or news_xpath or general_xpath) and confidence > 0.3:
                        all_investor_xpath.extend(investor_xpath)
                        all_news_xpath.extend(news_xpath)
                        all_general_xpath.extend(general_xpath)
                        total_confidence += confidence
                        valid_results += 1

                        logger.debug(f"分片 {i} 提取到: 投资者{len(investor_xpath)}个, 新闻{len(news_xpath)}个, 通用{len(general_xpath)}个, 置信度: {confidence:.2f}")
                    else:
                        logger.debug(f"分片 {i} 结果质量较低，跳过")
                else:
                    logger.warning(f"分片 {i} 分析失败")

            except Exception as e:
                logger.error(f"分析分片 {i} 时出错: {e}")
                continue

        # 合并结果
        if valid_results > 0:
            # 去重XPath规则（保持顺序）
            unique_investor_xpath = list(dict.fromkeys(all_investor_xpath))
            unique_news_xpath = list(dict.fromkeys(all_news_xpath))
            unique_general_xpath = list(dict.fromkeys(all_general_xpath))

            # 进一步优化：移除冗余和冲突的规则
            unique_investor_xpath = self._optimize_xpath_rules(unique_investor_xpath)
            unique_news_xpath = self._optimize_xpath_rules(unique_news_xpath)
            unique_general_xpath = self._optimize_xpath_rules(unique_general_xpath)

            # 确保不同类别之间没有重复
            unique_news_xpath = [rule for rule in unique_news_xpath if rule not in unique_investor_xpath]
            unique_general_xpath = [rule for rule in unique_general_xpath if rule not in unique_investor_xpath and rule not in unique_news_xpath]

            avg_confidence = total_confidence / valid_results

            logger.info(f"分批分类XPath分析完成:")
            logger.info(f"  投资者XPath: {len(all_investor_xpath)} -> {len(unique_investor_xpath)} 个唯一规则")
            logger.info(f"  新闻XPath: {len(all_news_xpath)} -> {len(unique_news_xpath)} 个唯一规则")
            logger.info(f"  通用XPath: {len(all_general_xpath)} -> {len(unique_general_xpath)} 个唯一规则")
            logger.info(f"  平均置信度: {avg_confidence:.2f}")

            return {
                'investor_xpath_rules': unique_investor_xpath,
                'news_xpath_rules': unique_news_xpath,
                'general_xpath_rules': unique_general_xpath,
                'confidence': avg_confidence,
                'page_type': page_type,
                'batch_info': {
                    'total_fragments': len(html_fragments),
                    'valid_results': valid_results,
                    'total_investor_found': len(all_investor_xpath),
                    'total_news_found': len(all_news_xpath),
                    'total_general_found': len(all_general_xpath),
                    'unique_investor': len(unique_investor_xpath),
                    'unique_news': len(unique_news_xpath),
                    'unique_general': len(unique_general_xpath)
                }
            }
        else:
            logger.warning("所有分片分类XPath分析都失败")
            return None
