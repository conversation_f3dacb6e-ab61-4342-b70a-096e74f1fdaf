#!/usr/bin/env python3
"""
使用真实公司数据测试新闻板块发掘功能
"""

import sys
import os
import time
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.search_research import SearchResearchClass
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

def test_with_real_company_news_sections(company_name: str = "TriSalus"):
    """
    使用真实公司测试新闻板块发掘功能
    
    Args:
        company_name: 要测试的公司名称
    """
    logger.info("=" * 80)
    logger.info(f"使用真实公司测试新闻板块发掘功能: {company_name}")
    logger.info("=" * 80)
    
    try:
        # 初始化搜索研究类
        research = SearchResearchClass()
        
        # 执行完整的公司调研流程（包含新闻板块发掘）
        logger.info(f"开始调研公司: {company_name}")
        start_time = time.time()
        
        result = research.research_company(company_name)
        
        end_time = time.time()
        duration = end_time - start_time
        
        logger.info(f"调研完成，耗时: {duration:.2f} 秒")
        
        # 分析结果
        if result.get("status") == "completed":
            logger.info("=" * 60)
            logger.info("新闻板块发掘结果分析")
            logger.info("=" * 60)
            
            # 基本信息
            logger.info(f"公司名称: {result.get('company_name', 'N/A')}")
            logger.info(f"官网URL: {result.get('base_url', 'N/A')}")
            
            # 分类的新闻板块页面
            investor_urls = result.get('investor_relations_urls', [])
            news_urls = result.get('news_section_urls', [])
            mixed_urls = result.get('mixed_section_urls', [])
            
            logger.info("=" * 40)
            logger.info("新闻板块分类结果")
            logger.info("=" * 40)
            
            logger.info(f"投资者关系页面数量: {len(investor_urls)}")
            for i, url in enumerate(investor_urls[:5], 1):  # 只显示前5个
                logger.info(f"  {i}. {url}")
            if len(investor_urls) > 5:
                logger.info(f"  ... 还有 {len(investor_urls) - 5} 个页面")
            
            logger.info(f"一般新闻页面数量: {len(news_urls)}")
            for i, url in enumerate(news_urls[:5], 1):  # 只显示前5个
                logger.info(f"  {i}. {url}")
            if len(news_urls) > 5:
                logger.info(f"  ... 还有 {len(news_urls) - 5} 个页面")
            
            logger.info(f"混合板块页面数量: {len(mixed_urls)}")
            for i, url in enumerate(mixed_urls[:5], 1):  # 只显示前5个
                logger.info(f"  {i}. {url}")
            if len(mixed_urls) > 5:
                logger.info(f"  ... 还有 {len(mixed_urls) - 5} 个页面")
            
            # 分类的XPath规则分析
            logger.info("=" * 40)
            logger.info("XPath规则分类结果")
            logger.info("=" * 40)
            
            if 'investor_xpath_rules' in result:
                investor_xpath = result.get('investor_xpath_rules', [])
                news_xpath = result.get('news_xpath_rules', [])
                general_xpath = result.get('general_xpath_rules', [])
                all_xpath = result.get('all_xpath_rules', [])
                
                logger.info(f"投资者XPath规则数量: {len(investor_xpath)}")
                logger.info(f"新闻XPath规则数量: {len(news_xpath)}")
                logger.info(f"通用XPath规则数量: {len(general_xpath)}")
                logger.info(f"总XPath规则数量: {len(all_xpath)}")
                
                logger.info("投资者XPath规则示例:")
                for i, rule in enumerate(investor_xpath[:3], 1):
                    logger.info(f"  {i}. {rule}")
                
                logger.info("新闻XPath规则示例:")
                for i, rule in enumerate(news_xpath[:3], 1):
                    logger.info(f"  {i}. {rule}")
                
                logger.info("通用XPath规则示例:")
                for i, rule in enumerate(general_xpath[:3], 1):
                    logger.info(f"  {i}. {rule}")
            else:
                # 向后兼容：显示原有格式
                all_xpath = result.get('all_xpath_rules', [])
                logger.info(f"XPath规则总数: {len(all_xpath)}")
                
                logger.info("XPath规则示例:")
                for i, rule in enumerate(all_xpath[:5], 1):
                    logger.info(f"  {i}. {rule}")
            
            # 保存结果到文件
            output_file = f"test_news_sections_result_{company_name.lower().replace(' ', '_')}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            logger.info(f"结果已保存到: {output_file}")
            
            # 功能验证统计
            logger.info("=" * 60)
            logger.info("功能验证统计")
            logger.info("=" * 60)
            
            total_sections = len(investor_urls) + len(news_urls) + len(mixed_urls)
            logger.info(f"总发现板块数: {total_sections}")
            logger.info(f"  - 投资者关系: {len(investor_urls)} ({len(investor_urls)/max(total_sections,1)*100:.1f}%)")
            logger.info(f"  - 一般新闻: {len(news_urls)} ({len(news_urls)/max(total_sections,1)*100:.1f}%)")
            logger.info(f"  - 混合板块: {len(mixed_urls)} ({len(mixed_urls)/max(total_sections,1)*100:.1f}%)")
            
            # 检查新功能是否生效
            has_new_structure = 'news_section_urls' in result or 'investor_xpath_rules' in result
            if has_new_structure:
                logger.info("✓ 新闻板块发掘功能已生效")
            else:
                logger.info("⚠ 使用了回退机制，新功能可能未完全生效")
            
            # 性能统计
            logger.info("=" * 60)
            logger.info("性能统计")
            logger.info("=" * 60)
            logger.info(f"总耗时: {duration:.2f} 秒")
            if total_sections > 0:
                logger.info(f"平均每个板块处理时间: {duration / total_sections:.2f} 秒")
            
        else:
            logger.error(f"调研失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    
    return True

def compare_old_vs_new():
    """
    比较新旧功能的差异
    """
    logger.info("=" * 80)
    logger.info("新旧功能对比分析")
    logger.info("=" * 80)
    
    logger.info("原有功能:")
    logger.info("1. 只能发现投资者关系板块")
    logger.info("2. XPath规则混合在一起，无分类")
    logger.info("3. 结果结构相对简单")
    logger.info("4. 主要面向投资者信息")
    
    logger.info("新增功能:")
    logger.info("1. 能够发现多种类型的新闻板块:")
    logger.info("   - 投资者关系板块")
    logger.info("   - 一般新闻板块")
    logger.info("   - 混合板块")
    logger.info("2. XPath规则按类型分类:")
    logger.info("   - 投资者XPath规则")
    logger.info("   - 新闻XPath规则")
    logger.info("   - 通用XPath规则")
    logger.info("3. 更丰富的结果结构")
    logger.info("4. 覆盖更全面的公司信息")
    
    logger.info("向后兼容性:")
    logger.info("✓ 保留所有原有字段")
    logger.info("✓ 原有代码无需修改")
    logger.info("✓ 提供回退机制")
    logger.info("✓ 支持渐进式升级")

def main():
    """主测试函数"""
    logger.info("开始真实公司新闻板块发掘测试")
    logger.info("测试时间: " + time.strftime("%Y-%m-%d %H:%M:%S"))
    
    # 检查配置
    from src.config import Config
    
    if not Config.GOOGLE_API_KEY or not Config.GOOGLE_CSE_ID:
        logger.warning("Google Search API配置缺失，可能影响测试结果")
    
    if not Config.OPENAI_API_KEY:
        logger.warning("OpenAI API配置缺失，可能影响测试结果")
    
    try:
        # 测试真实公司的新闻板块发掘
        success = test_with_real_company_news_sections("TriSalus")
        
        if success:
            logger.info("真实公司新闻板块发掘测试成功")
        else:
            logger.error("真实公司新闻板块发掘测试失败")
        
        # 功能对比分析
        compare_old_vs_new()
        
        logger.info("=" * 80)
        logger.info("所有测试完成")
        logger.info("=" * 80)
        
        return 0 if success else 1
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
