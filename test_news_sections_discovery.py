#!/usr/bin/env python3
"""
新闻板块发掘功能测试脚本
"""

import sys
import os
import time
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.search_research import SearchResearchClass
from src.services.ai_analyzer import AIAnalyzerService
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

def test_url_classification():
    """测试URL分类功能"""
    logger.info("=" * 60)
    logger.info("测试URL分类功能")
    logger.info("=" * 60)
    
    ai_analyzer = AIAnalyzerService()
    
    # 测试URL分类
    test_urls = [
        # 投资者关系URL
        ("https://example.com/investors", "investor"),
        ("https://example.com/ir/financials", "investor"),
        ("https://example.com/sec-filings", "investor"),
        ("https://example.com/shareholder-info", "investor"),
        ("https://example.com/quarterly-earnings", "investor"),
        
        # 新闻URL
        ("https://example.com/news", "news"),
        ("https://example.com/press-releases", "news"),
        ("https://example.com/media-center", "news"),
        ("https://example.com/company-blog", "news"),
        ("https://example.com/announcements", "news"),
        
        # 混合URL
        ("https://example.com/news-and-events", "mixed"),
        ("https://example.com/investor-news", "mixed"),
        ("https://example.com/press-and-investor", "mixed"),
        
        # 未知URL
        ("https://example.com/about", "unknown"),
        ("https://example.com/products", "unknown"),
        ("https://example.com/contact", "unknown")
    ]
    
    correct_classifications = 0
    total_tests = len(test_urls)
    
    logger.info(f"测试 {total_tests} 个URL的分类")
    
    for url, expected_type in test_urls:
        classified_type = ai_analyzer._classify_url_type(url)
        is_correct = classified_type == expected_type
        
        if is_correct:
            correct_classifications += 1
            status = "✓"
        else:
            status = "✗"
        
        logger.info(f"{status} {url} -> 预期: {expected_type}, 实际: {classified_type}")
    
    accuracy = (correct_classifications / total_tests) * 100
    logger.info(f"分类准确率: {accuracy:.1f}% ({correct_classifications}/{total_tests})")
    
    return accuracy > 80  # 期望准确率超过80%

def test_news_url_validation():
    """测试新闻URL验证功能"""
    logger.info("=" * 60)
    logger.info("测试新闻URL验证功能")
    logger.info("=" * 60)
    
    ai_analyzer = AIAnalyzerService()
    
    # 测试新闻URL验证
    valid_news_urls = [
        "https://example.com/news",
        "https://example.com/press-releases",
        "https://example.com/media-center",
        "https://example.com/company-updates",
        "https://example.com/latest-news",
        "https://example.com/newsroom",
        "https://example.com/blog/company-news"
    ]
    
    invalid_news_urls = [
        "https://example.com/about",
        "https://example.com/products",
        "https://example.com/contact",
        "https://example.com/careers",
        "invalid-url",
        "",
        "ftp://example.com/news"
    ]
    
    # 测试有效新闻URL
    valid_count = 0
    for url in valid_news_urls:
        is_valid = ai_analyzer._is_valid_news_url(url)
        if is_valid:
            valid_count += 1
            logger.info(f"✓ {url} - 有效新闻URL")
        else:
            logger.info(f"✗ {url} - 应该是有效新闻URL但被判定为无效")
    
    # 测试无效新闻URL
    invalid_count = 0
    for url in invalid_news_urls:
        is_valid = ai_analyzer._is_valid_news_url(url)
        if not is_valid:
            invalid_count += 1
            logger.info(f"✓ {url} - 正确识别为无效URL")
        else:
            logger.info(f"✗ {url} - 应该是无效URL但被判定为有效")
    
    total_valid = len(valid_news_urls)
    total_invalid = len(invalid_news_urls)
    
    logger.info(f"有效新闻URL识别率: {valid_count}/{total_valid} ({valid_count/total_valid*100:.1f}%)")
    logger.info(f"无效URL过滤率: {invalid_count}/{total_invalid} ({invalid_count/total_invalid*100:.1f}%)")
    
    return (valid_count / total_valid) > 0.8 and (invalid_count / total_invalid) > 0.8

def test_news_url_optimization():
    """测试新闻URL优化功能"""
    logger.info("=" * 60)
    logger.info("测试新闻URL优化功能")
    logger.info("=" * 60)
    
    ai_analyzer = AIAnalyzerService()
    
    # 测试URL优化
    test_urls = [
        "https://example.com/news",
        "https://example.com/news/",  # 重复（尾部斜杠）
        "https://example.com/press-releases",
        "https://example.com/press-releases",  # 完全重复
        "https://example.com/media-center?tab=news",
        "https://example.com/media-center?tab=events",  # 相似URL
        "https://example.com/newsroom",
        "invalid-url",  # 无效URL
        "",  # 空URL
        "https://example.com/company-blog",
        "https://example.com/announcements"
    ]
    
    logger.info(f"原始URL数量: {len(test_urls)}")
    logger.info("原始URL列表:")
    for i, url in enumerate(test_urls, 1):
        logger.info(f"  {i}. {url}")
    
    optimized_urls = ai_analyzer._optimize_news_urls(test_urls)
    
    logger.info(f"优化后URL数量: {len(optimized_urls)}")
    logger.info("优化后URL列表:")
    for i, url in enumerate(optimized_urls, 1):
        logger.info(f"  {i}. {url}")
    
    # 验证优化效果
    expected_reduction = len(test_urls) - len(optimized_urls)
    logger.info(f"减少了 {expected_reduction} 个URL")
    
    # 检查是否移除了无效URL
    has_invalid = any(url in optimized_urls for url in ["invalid-url", ""])
    if not has_invalid:
        logger.info("✓ 成功移除无效URL")
    else:
        logger.info("✗ 未能移除所有无效URL")
    
    return len(optimized_urls) < len(test_urls) and not has_invalid

def test_xpath_rule_optimization():
    """测试XPath规则优化功能"""
    logger.info("=" * 60)
    logger.info("测试XPath规则优化功能")
    logger.info("=" * 60)
    
    ai_analyzer = AIAnalyzerService()
    
    # 测试XPath规则优化
    test_xpath_rules = [
        "//div[@class='news-list']//a[@href]",
        "//div[@class='news-list']//a",  # 更通用的规则
        "//div[contains(@class, 'news')]//a[@href]",
        "//div[contains(@class, 'news')]//a[@href]",  # 完全重复
        "//section[@class='press-releases']//a",
        "//ul[@class='nav']//a[contains(@href, '/news')]",
        "",  # 空规则
        "//div[@class='news-list']//a[@href]",  # 重复
        "//article[@class='news-item']//h2//a[@href]",
        "//div[@id='newsroom']//li//a"
    ]
    
    logger.info(f"原始XPath规则数量: {len(test_xpath_rules)}")
    logger.info("原始XPath规则:")
    for i, rule in enumerate(test_xpath_rules, 1):
        logger.info(f"  {i}. {rule}")
    
    optimized_xpath = ai_analyzer._optimize_xpath_rules(test_xpath_rules)
    
    logger.info(f"优化后XPath规则数量: {len(optimized_xpath)}")
    logger.info("优化后XPath规则:")
    for i, rule in enumerate(optimized_xpath, 1):
        logger.info(f"  {i}. {rule}")
    
    # 验证优化效果
    expected_reduction = len(test_xpath_rules) - len(optimized_xpath)
    logger.info(f"减少了 {expected_reduction} 个XPath规则")
    
    # 检查是否移除了空规则
    has_empty = any(not rule.strip() for rule in optimized_xpath)
    if not has_empty:
        logger.info("✓ 成功移除空规则")
    else:
        logger.info("✗ 未能移除所有空规则")
    
    return len(optimized_xpath) < len(test_xpath_rules) and not has_empty

def test_complete_workflow():
    """测试完整的新闻板块发掘工作流程"""
    logger.info("=" * 60)
    logger.info("测试完整的新闻板块发掘工作流程")
    logger.info("=" * 60)
    
    # 注意：这里只是测试结构，实际运行需要有效的API密钥和网络连接
    logger.info("注意: 完整工作流程测试需要:")
    logger.info("1. 有效的Google Search API密钥")
    logger.info("2. 有效的OpenAI API密钥")
    logger.info("3. 网络连接")
    logger.info("当前仅测试代码结构和逻辑")
    
    try:
        research = SearchResearchClass()
        logger.info("SearchResearchClass 初始化成功")
        
        # 测试新方法是否存在
        if hasattr(research, '_extract_news_sections'):
            logger.info("✓ _extract_news_sections 方法存在")
        else:
            logger.error("✗ _extract_news_sections 方法不存在")
            return False
        
        if hasattr(research, '_extract_classified_xpath_rules'):
            logger.info("✓ _extract_classified_xpath_rules 方法存在")
        else:
            logger.error("✗ _extract_classified_xpath_rules 方法不存在")
            return False
        
        # 测试AI分析器的新方法
        ai_analyzer = research.ai_analyzer
        
        if hasattr(ai_analyzer, 'analyze_news_sections'):
            logger.info("✓ analyze_news_sections 方法存在")
        else:
            logger.error("✗ analyze_news_sections 方法不存在")
            return False
        
        if hasattr(ai_analyzer, 'analyze_classified_news_xpath'):
            logger.info("✓ analyze_classified_news_xpath 方法存在")
        else:
            logger.error("✗ analyze_classified_news_xpath 方法不存在")
            return False
        
        logger.info("所有新方法都已正确实现")
        return True
        
    except Exception as e:
        logger.error(f"工作流程测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始新闻板块发掘功能测试")
    logger.info("测试时间: " + time.strftime("%Y-%m-%d %H:%M:%S"))
    
    test_results = []
    
    try:
        # 测试URL分类功能
        result1 = test_url_classification()
        test_results.append(("URL分类功能", result1))
        
        # 测试新闻URL验证功能
        result2 = test_news_url_validation()
        test_results.append(("新闻URL验证功能", result2))
        
        # 测试新闻URL优化功能
        result3 = test_news_url_optimization()
        test_results.append(("新闻URL优化功能", result3))
        
        # 测试XPath规则优化功能
        result4 = test_xpath_rule_optimization()
        test_results.append(("XPath规则优化功能", result4))
        
        # 测试完整工作流程
        result5 = test_complete_workflow()
        test_results.append(("完整工作流程", result5))
        
        # 汇总测试结果
        logger.info("=" * 60)
        logger.info("测试结果汇总")
        logger.info("=" * 60)
        
        passed_tests = 0
        total_tests = len(test_results)
        
        for test_name, result in test_results:
            status = "✓ 通过" if result else "✗ 失败"
            logger.info(f"{status} {test_name}")
            if result:
                passed_tests += 1
        
        success_rate = (passed_tests / total_tests) * 100
        logger.info(f"测试通过率: {success_rate:.1f}% ({passed_tests}/{total_tests})")
        
        if success_rate >= 80:
            logger.info("新闻板块发掘功能测试整体通过")
            return 0
        else:
            logger.error("新闻板块发掘功能测试存在问题")
            return 1
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
