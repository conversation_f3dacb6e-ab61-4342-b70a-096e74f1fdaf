"""
核心搜索调研类
实现公司调研的完整流程
"""
import time
from typing import Dict, List, Any, Optional
from urllib.parse import urljoin

from src.config import Config
from src.utils.logger import setup_logger
from src.utils.prompt_manager import PromptManager
from src.utils.validators import DataValidator, validate_input_data, COMPANY_RESEARCH_SCHEMA
from src.utils.exceptions import SearchError, ScrapingError, AIAnalysisError, ValidationError
from src.utils.retry_decorator import retry_on_exception
from src.services.google_search import GoogleSearchService
from src.services.web_scraper import WebScraperService
from src.services.ai_analyzer import AIAnalyzerService

logger = setup_logger(__name__)

class SearchResearchClass:
    """公司搜索调研类"""
    
    def __init__(self):
        """初始化搜索调研类"""
        self.prompt_manager = PromptManager()
        self.google_search = GoogleSearchService()
        self.web_scraper = WebScraperService()
        self.ai_analyzer = AIAnalyzerService()
        
        logger.info("SearchResearchClass 初始化完成")
    
    def research_company(self, company_name: str) -> Dict[str, Any]:
        """
        执行完整的公司调研流程

        Args:
            company_name: 公司名称

        Returns:
            结构化的调研结果
        """
        logger.info(f"开始调研公司: {company_name}")

        try:
            # 输入验证
            validate_input_data({'company_name': company_name}, COMPANY_RESEARCH_SCHEMA)

            # 清理公司名称
            cleaned_company_name = DataValidator.sanitize_company_name(company_name)
            if not cleaned_company_name:
                raise ValidationError("公司名称无效或为空")

            # 初始化结果结构
            result = {
                "company_name": cleaned_company_name,
                "base_url": "",
                "investor_relations_urls": [],
                "news_xpath_rules": [],
                "research_timestamp": time.time(),
                "status": "processing"
            }
            
            # 步骤1: 搜索公司官网
            logger.info("步骤1: 搜索公司官网")
            base_url = self._search_official_website(cleaned_company_name)
            if not base_url:
                result["status"] = "failed"
                result["error"] = "无法找到公司官网"
                return result

            result["base_url"] = base_url
            logger.info(f"找到官网: {base_url}")
            
            # 步骤2: 分析官网首页，提取新闻板块页面（包括投资者关系和一般新闻）
            logger.info("步骤2: 分析官网首页，发掘新闻板块")
            news_sections_result = self._extract_news_sections(cleaned_company_name, base_url)

            # 处理新闻板块结果
            if news_sections_result:
                result["investor_relations_urls"] = news_sections_result.get("investor_relations_urls", [])
                result["news_section_urls"] = news_sections_result.get("news_section_urls", [])
                result["mixed_section_urls"] = news_sections_result.get("mixed_section_urls", [])

                logger.info(f"找到投资者关系页面: {len(result['investor_relations_urls'])}个")
                logger.info(f"找到一般新闻页面: {len(result['news_section_urls'])}个")
                logger.info(f"找到混合板块页面: {len(result['mixed_section_urls'])}个")
            else:
                # 如果新方法失败，回退到原有方法
                logger.warning("新闻板块分析失败，回退到原有投资者关系分析")
                investor_urls = self._extract_investor_relations_urls(cleaned_company_name, base_url)
                result["investor_relations_urls"] = investor_urls
                result["news_section_urls"] = []
                result["mixed_section_urls"] = []
                logger.info(f"找到投资者关系页面: {len(investor_urls)}个")

            # 步骤3: 分析各类新闻板块页面，提取分类的XPath规则
            logger.info("步骤3: 分析新闻板块页面，提取分类XPath规则")

            # 收集所有需要分析的页面
            all_pages_to_analyze = []

            # 投资者关系页面
            for url in result.get("investor_relations_urls", []):
                all_pages_to_analyze.append((url, "investor_relations"))

            # 一般新闻页面
            for url in result.get("news_section_urls", []):
                all_pages_to_analyze.append((url, "news_section"))

            # 混合板块页面
            for url in result.get("mixed_section_urls", []):
                all_pages_to_analyze.append((url, "mixed_section"))

            # 提取分类的XPath规则
            xpath_results = self._extract_classified_xpath_rules(cleaned_company_name, all_pages_to_analyze)

            # 处理XPath结果
            if xpath_results:
                result["investor_xpath_rules"] = xpath_results.get("investor_xpath_rules", [])
                result["news_xpath_rules"] = xpath_results.get("news_xpath_rules", [])
                result["general_xpath_rules"] = xpath_results.get("general_xpath_rules", [])

                # 向后兼容：合并所有规则
                all_rules = (result["investor_xpath_rules"] +
                           result["news_xpath_rules"] +
                           result["general_xpath_rules"])
                result["all_xpath_rules"] = list(dict.fromkeys(all_rules))  # 去重

                # 添加新的xpath与URL绑定信息
                result["url_xpath_mapping"] = xpath_results.get("url_xpath_mapping", {})
                result["xpath_statistics"] = xpath_results.get("statistics", {})

                # 如果有完整的xpath结果对象，也保存它（用于调试和进一步分析）
                if "xpath_result_object" in xpath_results:
                    result["_xpath_result_object"] = xpath_results["xpath_result_object"]

                logger.info(f"提取到投资者XPath规则: {len(result['investor_xpath_rules'])}个")
                logger.info(f"提取到新闻XPath规则: {len(result['news_xpath_rules'])}个")
                logger.info(f"提取到通用XPath规则: {len(result['general_xpath_rules'])}个")
                logger.info(f"总计XPath规则: {len(result['all_xpath_rules'])}个")
                logger.info(f"涉及URL数量: {len(result['url_xpath_mapping'])}个")
            else:
                # 如果新方法失败，回退到原有方法
                logger.warning("分类XPath分析失败，回退到原有方法")
                xpath_result = self._extract_news_xpath_rules(cleaned_company_name, result.get("investor_relations_urls", []))

                if isinstance(xpath_result, dict):
                    result["primary_xpath"] = xpath_result.get("primary_xpath", [])
                    result["xpath_rules"] = xpath_result.get("xpath_rules", [])
                    result["all_xpath_rules"] = xpath_result.get("all_rules", [])
                else:
                    result["all_xpath_rules"] = xpath_result if xpath_result else []

                logger.info(f"回退方法提取到XPath规则: {len(result.get('all_xpath_rules', []))}个")
            
            result["status"] = "completed"
            logger.info(f"公司调研完成: {company_name}")
            
            return result
            
        except ValidationError as e:
            logger.error(f"公司调研输入验证失败: {company_name}, 错误: {e}")
            return {
                "company_name": company_name,
                "status": "failed",
                "error": f"输入验证失败: {str(e)}",
                "research_timestamp": time.time()
            }
        except Exception as e:
            logger.error(f"公司调研失败: {company_name}, 错误: {e}")
            return {
                "company_name": company_name,
                "status": "failed",
                "error": str(e),
                "research_timestamp": time.time()
            }

    @retry_on_exception(max_retries=2, delay=1.0, exceptions=(SearchError, AIAnalysisError))
    def _search_official_website(self, company_name: str) -> Optional[str]:
        """
        搜索公司官方网站
        
        Args:
            company_name: 公司名称
            
        Returns:
            官网URL，如果未找到返回None
        """
        try:
            # 执行Google搜索
            search_results = self.google_search.search(company_name)
            if not search_results:
                logger.warning(f"Google搜索无结果: {company_name}")
                raise SearchError(f"Google搜索无结果: {company_name}")
            
            # 使用AI分析搜索结果
            result_data = self.ai_analyzer.analyze_search_results(company_name, search_results)

            if result_data:
                official_website = result_data.get('official_website')
                confidence = result_data.get('confidence', 0)

                if official_website and confidence > 0.5:
                    return official_website
                else:
                    logger.warning(f"AI分析置信度过低: {confidence}")
                    raise AIAnalysisError(f"AI分析置信度过低: {confidence}")
            else:
                logger.error("AI分析搜索结果失败")
                raise AIAnalysisError("AI分析搜索结果失败")

        except (SearchError, AIAnalysisError):
            raise  # 重新抛出，让重试装饰器处理
        except Exception as e:
            logger.error(f"搜索官网失败: {e}")
            raise SearchError(f"搜索官网失败: {e}")

    @retry_on_exception(max_retries=2, delay=1.0, exceptions=(ScrapingError, AIAnalysisError))
    def _extract_investor_relations_urls(self, company_name: str, base_url: str) -> List[str]:
        """
        从官网首页提取投资者关系页面URL

        Args:
            company_name: 公司名称
            base_url: 官网首页URL

        Returns:
            投资者关系页面URL列表
        """
        try:
            # 抓取首页HTML内容
            html_content = self.web_scraper.fetch_page(base_url)
            if not html_content:
                logger.warning(f"无法获取首页内容: {base_url}")
                raise ScrapingError(f"无法获取首页内容: {base_url}")

            # 清理HTML内容
            cleaned_html = self.web_scraper.clean_html_content(html_content)

            # 检查是否需要分批处理
            if len(cleaned_html) > 50000:
                logger.info(f"HTML内容过长({len(cleaned_html):,}字符)，使用分批处理")

                # 分割HTML内容
                html_fragments = self.web_scraper.split_html_content(cleaned_html, max_length=50000)

                # 使用分批AI分析
                result_data = self.ai_analyzer.analyze_investor_relations_batch(
                    company_name, base_url, html_fragments
                )
            else:
                logger.info(f"HTML内容适中({len(cleaned_html):,}字符)，使用单次处理")
                # 使用单次AI分析
                result_data = self.ai_analyzer.analyze_investor_relations(
                    company_name, base_url, cleaned_html
                )

            if result_data:
                urls = result_data.get('investor_relations_urls', [])
                confidence = result_data.get('confidence', 0)
                batch_info = result_data.get('batch_info', {})

                if confidence > 0.3:  # 较低的置信度阈值
                    # 处理相对链接
                    processed_urls = []
                    for url in urls:
                        if url.startswith('http'):
                            processed_urls.append(url)
                        else:
                            absolute_url = urljoin(base_url, url)
                            processed_urls.append(absolute_url)

                    # 记录批处理信息
                    if batch_info:
                        logger.info(f"分批处理结果: {batch_info}")

                    return processed_urls
                else:
                    logger.warning(f"投资者关系页面分析置信度过低: {confidence}")
                    raise AIAnalysisError(f"投资者关系页面分析置信度过低: {confidence}")
            else:
                logger.error("AI分析投资者关系页面失败")
                raise AIAnalysisError("AI分析投资者关系页面失败")

        except (ScrapingError, AIAnalysisError):
            raise  # 重新抛出，让重试装饰器处理
        except Exception as e:
            logger.error(f"提取投资者关系页面失败: {e}")
            raise ScrapingError(f"提取投资者关系页面失败: {e}")

    @retry_on_exception(max_retries=1, delay=2.0, exceptions=(ScrapingError, AIAnalysisError))
    def _extract_news_xpath_rules(self, company_name: str, investor_urls: List[str]) -> List[str]:
        """
        从投资者关系页面提取新闻链接的XPath规则

        Args:
            company_name: 公司名称
            investor_urls: 投资者关系页面URL列表

        Returns:
            XPath规则列表
        """
        all_primary_xpath = []
        all_xpath_rules = []

        for url in investor_urls:
            try:
                logger.info(f"分析投资者关系页面: {url}")

                # 抓取页面HTML内容
                html_content = self.web_scraper.fetch_page(url)
                if not html_content:
                    logger.warning(f"无法获取页面内容: {url}")
                    raise ScrapingError(f"无法获取页面内容: {url}")

                # 清理HTML内容
                cleaned_html = self.web_scraper.clean_html_content(html_content)

                # 检查是否需要分批处理
                if len(cleaned_html) > 50000:
                    logger.info(f"页面内容过长({len(cleaned_html):,}字符)，使用分批处理: {url}")

                    # 分割HTML内容
                    html_fragments = self.web_scraper.split_html_content(cleaned_html, max_length=50000)

                    # 使用分批AI分析
                    result_data = self.ai_analyzer.analyze_news_xpath_batch(
                        company_name, url, html_fragments
                    )
                else:
                    logger.info(f"页面内容适中({len(cleaned_html):,}字符)，使用单次处理: {url}")
                    # 使用单次AI分析
                    result_data = self.ai_analyzer.analyze_news_xpath(
                        company_name, url, cleaned_html
                    )

                if result_data:
                    # 处理分批结果和单次结果的不同结构
                    if 'primary_xpath' in result_data:
                        # 分批处理结果
                        primary_xpath = result_data.get('primary_xpath', [])
                        xpath_rules = result_data.get('xpath_rules', [])
                        confidence = result_data.get('confidence', 0)
                        batch_info = result_data.get('batch_info', {})

                        if confidence > 0.4 and (primary_xpath or xpath_rules):
                            all_primary_xpath.extend(primary_xpath)
                            all_xpath_rules.extend(xpath_rules)
                            logger.info(f"从 {url} 提取到 {len(primary_xpath)} 个主要XPath，{len(xpath_rules)} 个其他规则")

                            if batch_info:
                                logger.info(f"分批处理结果: {batch_info}")
                        else:
                            logger.warning(f"XPath规则提取置信度过低: {confidence}")
                            raise AIAnalysisError(f"XPath规则提取置信度过低: {confidence}")
                    else:
                        # 单次处理结果
                        xpath_rules = result_data.get('xpath_rules', [])
                        confidence = result_data.get('confidence', 0)

                        if confidence > 0.5 and xpath_rules:
                            all_xpath_rules.extend(xpath_rules)
                            logger.info(f"从 {url} 提取到 {len(xpath_rules)} 个XPath规则")
                        else:
                            logger.warning(f"XPath规则提取置信度过低: {confidence}")
                            raise AIAnalysisError(f"XPath规则提取置信度过低: {confidence}")
                else:
                    logger.error(f"AI分析XPath规则失败: {url}")
                    raise AIAnalysisError(f"AI分析XPath规则失败: {url}")

            except (ScrapingError, AIAnalysisError) as e:
                logger.warning(f"分析投资者关系页面失败: {url}, 错误: {e}")
                continue  # 继续处理下一个URL
            except Exception as e:
                logger.error(f"分析投资者关系页面发生未知错误: {url}, 错误: {e}")
                continue

            # 添加延迟避免请求过快
            time.sleep(Config.REQUEST_DELAY)

        # 去重并合并结果
        unique_primary_xpath = list(dict.fromkeys(all_primary_xpath))  # 保持顺序的去重
        unique_xpath_rules = list(dict.fromkeys(all_xpath_rules))

        # 合并所有规则（为了向后兼容）
        all_combined_rules = unique_primary_xpath + unique_xpath_rules

        logger.info(f"XPath规则提取完成:")
        logger.info(f"  主要XPath: {len(unique_primary_xpath)} 个")
        logger.info(f"  其他规则: {len(unique_xpath_rules)} 个")
        logger.info(f"  总计: {len(all_combined_rules)} 个唯一规则")

        # 返回结构化结果
        return {
            'primary_xpath': unique_primary_xpath,
            'xpath_rules': unique_xpath_rules,
            'all_rules': all_combined_rules  # 向后兼容
        }

    @retry_on_exception(max_retries=2, delay=1.0, exceptions=(SearchError, AIAnalysisError))
    def _extract_news_sections(self, company_name: str, base_url: str) -> Optional[Dict[str, Any]]:
        """
        从官网首页提取新闻板块页面URL（包括投资者关系、一般新闻和混合板块）

        Args:
            company_name: 公司名称
            base_url: 官网首页URL

        Returns:
            新闻板块分析结果字典
        """
        try:
            # 抓取首页HTML内容
            html_content = self.web_scraper.fetch_page(base_url)
            if not html_content:
                logger.warning(f"无法获取首页内容: {base_url}")
                raise ScrapingError(f"无法获取首页内容: {base_url}")

            # 清理HTML内容
            cleaned_html = self.web_scraper.clean_html_content(html_content)

            # 检查是否需要分批处理
            if len(cleaned_html) > 50000:
                logger.info(f"HTML内容过长({len(cleaned_html):,}字符)，使用分批处理")

                # 分割HTML内容
                html_fragments = self.web_scraper.split_html_content(cleaned_html, max_length=50000)

                # 使用分批AI分析
                result_data = self.ai_analyzer.analyze_news_sections_batch(
                    company_name, base_url, html_fragments
                )
            else:
                logger.info(f"HTML内容适中({len(cleaned_html):,}字符)，使用单次处理")
                # 使用单次AI分析
                result_data = self.ai_analyzer.analyze_news_sections(
                    company_name, base_url, cleaned_html
                )

            if result_data:
                investor_urls = result_data.get('investor_relations_urls', [])
                news_urls = result_data.get('news_section_urls', [])
                mixed_urls = result_data.get('mixed_section_urls', [])
                confidence = result_data.get('confidence', 0)
                batch_info = result_data.get('batch_info', {})

                if confidence > 0.3:  # 较低的置信度阈值
                    # 处理相对链接
                    processed_investor_urls = []
                    processed_news_urls = []
                    processed_mixed_urls = []

                    for url in investor_urls:
                        if url.startswith('http'):
                            processed_investor_urls.append(url)
                        else:
                            absolute_url = urljoin(base_url, url)
                            processed_investor_urls.append(absolute_url)

                    for url in news_urls:
                        if url.startswith('http'):
                            processed_news_urls.append(url)
                        else:
                            absolute_url = urljoin(base_url, url)
                            processed_news_urls.append(absolute_url)

                    for url in mixed_urls:
                        if url.startswith('http'):
                            processed_mixed_urls.append(url)
                        else:
                            absolute_url = urljoin(base_url, url)
                            processed_mixed_urls.append(absolute_url)

                    # 记录批处理信息
                    if batch_info:
                        logger.info(f"分批处理结果: {batch_info}")

                    return {
                        'investor_relations_urls': processed_investor_urls,
                        'news_section_urls': processed_news_urls,
                        'mixed_section_urls': processed_mixed_urls,
                        'confidence': confidence,
                        'batch_info': batch_info
                    }
                else:
                    logger.warning(f"新闻板块分析置信度过低: {confidence}")
                    raise AIAnalysisError(f"新闻板块分析置信度过低: {confidence}")
            else:
                logger.error("AI分析新闻板块失败")
                raise AIAnalysisError("AI分析新闻板块失败")

        except (ScrapingError, AIAnalysisError):
            raise  # 重新抛出，让重试装饰器处理
        except Exception as e:
            logger.error(f"提取新闻板块失败: {e}")
            raise ScrapingError(f"提取新闻板块失败: {e}")

    @retry_on_exception(max_retries=1, delay=2.0, exceptions=(ScrapingError, AIAnalysisError))
    def _extract_classified_xpath_rules(self, company_name: str, pages_to_analyze: List[tuple]) -> Optional[Dict[str, Any]]:
        """
        从各类新闻板块页面提取分类的XPath规则

        Args:
            company_name: 公司名称
            pages_to_analyze: 要分析的页面列表，每个元素为(url, page_type)元组

        Returns:
            分类的XPath规则字典，包含xpath与URL的绑定关系
        """
        from src.models.xpath_result import ClassifiedXPathResult

        # 使用新的数据结构管理xpath规则
        xpath_result = ClassifiedXPathResult()

        for url, page_type in pages_to_analyze:
            try:
                logger.info(f"分析{page_type}页面: {url}")

                # 抓取页面HTML内容
                html_content = self.web_scraper.fetch_page(url)
                if not html_content:
                    logger.warning(f"无法获取页面内容: {url}")
                    raise ScrapingError(f"无法获取页面内容: {url}")

                # 清理HTML内容
                cleaned_html = self.web_scraper.clean_html_content(html_content)

                # 检查是否需要分批处理
                if len(cleaned_html) > 50000:
                    logger.info(f"页面内容过长({len(cleaned_html):,}字符)，使用分批处理: {url}")

                    # 分割HTML内容
                    html_fragments = self.web_scraper.split_html_content(cleaned_html, max_length=50000)

                    # 使用分批AI分析
                    result_data = self.ai_analyzer.analyze_classified_news_xpath_batch(
                        company_name, url, page_type, html_fragments
                    )
                else:
                    logger.info(f"页面内容适中({len(cleaned_html):,}字符)，使用单次处理: {url}")
                    # 使用单次AI分析
                    result_data = self.ai_analyzer.analyze_classified_news_xpath(
                        company_name, url, page_type, cleaned_html
                    )

                if result_data:
                    investor_xpath = result_data.get('investor_xpath_rules', [])
                    news_xpath = result_data.get('news_xpath_rules', [])
                    general_xpath = result_data.get('general_xpath_rules', [])
                    confidence = result_data.get('confidence', 0)
                    batch_info = result_data.get('batch_info', {})

                    if confidence > 0.4 and (investor_xpath or news_xpath or general_xpath):
                        # 使用新的数据结构添加规则，保持与URL的绑定关系
                        xpath_result.add_rules(investor_xpath, url, page_type, 'investor', confidence)
                        xpath_result.add_rules(news_xpath, url, page_type, 'news', confidence)
                        xpath_result.add_rules(general_xpath, url, page_type, 'general', confidence)

                        logger.info(f"从 {url} 提取到: 投资者{len(investor_xpath)}个, 新闻{len(news_xpath)}个, 通用{len(general_xpath)}个XPath规则")

                        if batch_info:
                            logger.info(f"分批处理结果: {batch_info}")
                    else:
                        logger.warning(f"分类XPath规则提取置信度过低: {confidence}")
                        raise AIAnalysisError(f"分类XPath规则提取置信度过低: {confidence}")
                else:
                    logger.error(f"AI分析分类XPath规则失败: {url}")
                    raise AIAnalysisError(f"AI分析分类XPath规则失败: {url}")

            except (ScrapingError, AIAnalysisError) as e:
                logger.warning(f"分析{page_type}页面失败: {url}, 错误: {e}")
                continue  # 继续处理下一个URL
            except Exception as e:
                logger.error(f"分析{page_type}页面发生未知错误: {url}, 错误: {e}")
                continue

            # 添加延迟避免请求过快
            time.sleep(Config.REQUEST_DELAY)

        # 去重并合并结果
        xpath_result.deduplicate()
        xpath_result.remove_cross_category_duplicates()

        # 获取最终的规则列表
        legacy_format = xpath_result.get_legacy_format()
        unique_investor_xpath = legacy_format['investor_xpath_rules']
        unique_news_xpath = legacy_format['news_xpath_rules']
        unique_general_xpath = legacy_format['general_xpath_rules']

        # 获取统计信息
        stats = xpath_result.get_statistics()

        logger.info(f"分类XPath规则提取完成:")
        logger.info(f"  投资者XPath: {stats['investor_rules_count']} 个")
        logger.info(f"  新闻XPath: {stats['news_rules_count']} 个")
        logger.info(f"  通用XPath: {stats['general_rules_count']} 个")
        logger.info(f"  涉及URL数量: {stats['unique_urls']} 个")

        # 返回结构化结果，包含xpath与URL的绑定信息
        result = {
            'investor_xpath_rules': unique_investor_xpath,
            'news_xpath_rules': unique_news_xpath,
            'general_xpath_rules': unique_general_xpath,
            'xpath_result_object': xpath_result,  # 包含完整的绑定信息
            'url_xpath_mapping': xpath_result.get_url_xpath_mapping(),
            'statistics': stats
        }

        return result
